<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyTube Shelf - Save & Organize Videos</title>

    <!-- FONT AWESOME -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- LIBRARIES -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script> <!-- Swiper (can be used for carousels if needed) -->
    <script src="https://cdn.jsdelivr.net/npm/motion@10.17.0/dist/motion.umd.min.js"></script> <!-- Motion One -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script> <!-- <PERSON><PERSON> Player -->
    
    <!-- NOTE: Barba.js is for multi-page transitions. We will simulate its fluid effect with CSS transitions in this single-page app. -->
    
    <style>
        /* --- GLOBAL STYLES & VARIABLES --- */
        :root {
            --bg-color: linear-gradient(135deg, #e8e9f0 0%, #f0f1f8 50%, #f8f9fc 100%);
            --surface-color: rgba(255, 255, 255, 0.9);
            --primary-color: linear-gradient(135deg, #6b73a3 0%, #8b94c7 100%);
            --primary-hover: linear-gradient(135deg, #8b94c7 0%, #6b73a3 100%);
            --secondary-gradient: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
            --text-color: #4a4a6a;
            --text-muted: rgba(74, 74, 106, 0.6);
            --text-light: #a8a8c0;
            --card-bg: rgba(255, 255, 255, 0.95);
            --card-hover-bg: rgba(255, 255, 255, 1);
            --border-color: rgba(107, 115, 163, 0.15);
            --shadow-color: rgba(107, 115, 163, 0.15);
            --modal-bg: rgba(232, 233, 240, 0.95);
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(107, 115, 163, 0.1);

            --border-radius-sm: 16px;
            --border-radius-md: 24px;
            --border-radius-lg: 32px;
            --padding-md: 20px;
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            overscroll-behavior: none;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(107, 115, 163, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 157, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(139, 148, 199, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* --- LAYOUT & MAIN APP STYLING --- */
        #app {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        
        .main-container {
            padding: var(--padding-md);
            flex-grow: 1;
            transition: opacity 0.3s ease-in-out;
        }

        header.app-header {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px 24px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 8px 32px var(--shadow-color);
        }

        .logo {
            font-size: 1.6rem;
            font-weight: 700;
            background: var(--primary-color);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .logo .fa-youtube {
            color: #ff4757;
            filter: drop-shadow(0 0 8px rgba(255, 71, 87, 0.5));
            -webkit-text-fill-color: #ff4757;
        }

        .search-bar {
            flex-grow: 1;
            position: relative;
        }

        .search-bar .fa-search {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .search-bar:focus-within .fa-search {
            color: rgba(102, 126, 234, 0.8);
            transform: translateY(-50%) scale(1.1);
        }
        
        #searchInput {
            width: 100%;
            padding: 16px 20px 16px 50px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            color: var(--text-color);
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        #searchInput:focus {
            outline: none;
            border-color: rgba(102, 126, 234, 0.6);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2), 0 8px 25px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }

        .controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .view-controls {
            display: flex;
            gap: 8px;
        }
        
        .view-controls .btn-icon, .btn-primary {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            color: var(--text-color);
            padding: 12px 16px;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 1rem;
            line-height: 1;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }

        .view-controls .btn-icon:hover, .btn-primary:hover {
            background: var(--primary-color);
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .view-controls .btn-icon.active {
            background: var(--primary-color);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            padding: 14px 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            border-radius: var(--border-radius-sm);
            position: relative;
            overflow: hidden;
        }
        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        /* --- VIDEO GRID & VIEW MODES --- */
        #video-grid {
            display: grid;
            gap: 20px;
            padding-top: 20px;
        }
        
        /* Large Icons */
        #video-grid.large-icons { grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); }
        
        /* Small Icons */
        #video-grid.small-icons { grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); }
        #video-grid.small-icons .video-card .card-title { font-size: 0.9rem; }

        /* List View */
        #video-grid.list-view { grid-template-columns: 1fr; }
        #video-grid.list-view .video-card { display: flex; align-items: center; gap: 15px; padding: 10px; }
        #video-grid.list-view .video-card .card-thumbnail { width: 120px; height: 67px; flex-shrink: 0; }
        #video-grid.list-view .video-card .card-content { flex-grow: 1; }
        #video-grid.list-view .video-card .card-tags { display: none; } /* Hide tags for cleaner list */

        /* Detailed View */
        #video-grid.detailed-view { grid-template-columns: 1fr; }
        #video-grid.detailed-view .video-card { display: flex; gap: 15px; flex-direction: column; }
        #video-grid.detailed-view .video-card .card-thumbnail { aspect-ratio: 16 / 9; width: 100%; height: auto; }
        
        /* Tiles View */
        #video-grid.tiles-view { grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); }
        #video-grid.tiles-view .video-card { padding: 0; overflow: hidden; }
        #video-grid.tiles-view .video-card .card-content { 
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);
            padding: 10px;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s, transform 0.3s;
        }
        #video-grid.tiles-view .video-card:hover .card-content { opacity: 1; transform: translateY(0); }
        #video-grid.tiles-view .video-card .card-title { color: white; }
        #video-grid.tiles-view .video-card .card-tags { display: none; }


        /* --- VIDEO CARD --- */
        .video-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            cursor: pointer;
            position: relative;
            box-shadow: 0 8px 25px var(--shadow-color);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--glass-border);
        }

        .video-card:hover {
            background: var(--card-hover-bg);
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            border-color: rgba(102, 126, 234, 0.3);
        }
        .card-thumbnail {
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }

        .card-thumbnail::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s;
            z-index: 1;
        }

        .video-card:hover .card-thumbnail::before {
            transform: translateX(100%);
        }

        .card-thumbnail img {
            width: 100%;
            height: auto;
            display: block;
            aspect-ratio: 16 / 9;
            object-fit: cover;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
        }
        .video-card:hover .card-thumbnail img {
             transform: scale(1.1);
        }

        .card-content {
            padding: var(--padding-md);
            position: relative;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .card-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .tag {
            background: var(--secondary-gradient);
            color: white;
            font-size: 0.75rem;
            font-weight: 500;
            padding: 6px 12px;
            border-radius: var(--border-radius-sm);
            box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
            transition: all 0.3s ease;
        }

        .tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);
        }

        .placeholder-message {
            text-align: center;
            padding: 80px 20px;
            color: var(--text-muted);
            grid-column: 1 / -1; /* Span all columns */
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            margin: 20px;
        }
        .placeholder-message i {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            background: var(--secondary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: pulse 2s ease-in-out infinite;
        }
        .placeholder-message h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.7) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* --- MODAL STYLES --- */
        .modal {
            position: fixed;
            inset: 0;
            background: var(--modal-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal.is-visible {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            transform: scale(0.8) translateY(50px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal.is-visible .modal-content {
            transform: scale(1) translateY(0);
        }

        .modal-header {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-muted);
            font-size: 1.5rem;
            cursor: pointer;
            transition: color 0.2s;
        }
        .modal-close:hover {
            color: var(--text-color);
        }
        
        .modal-body {
            padding: var(--padding-md);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-muted);
        }

        .form-group input {
            width: 100%;
            padding: 16px 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-sm);
            color: var(--text-color);
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-group input:focus {
            outline: none;
            border-color: rgba(102, 126, 234, 0.6);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }
        
        .video-preview {
            display: flex;
            gap: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: var(--padding-md);
            border-radius: var(--border-radius-sm);
            align-items: center;
            min-height: 100px;
            border: 2px dashed var(--glass-border);
            transition: all 0.3s ease;
        }

        .video-preview:hover {
            border-color: rgba(102, 126, 234, 0.4);
            background: var(--card-hover-bg);
        }
        .video-preview img {
            width: 140px;
            height: 78px;
            border-radius: var(--border-radius-sm);
            object-fit: cover;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        .video-preview p {
            flex: 1;
            font-weight: 500;
        }

        #lottie-loader {
            width: 60px;
            height: 60px;
            margin: auto;
        }

        .tag-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .tag-suggestion {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            cursor: pointer;
        }
        .tag-suggestion:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .modal-footer {
            padding: var(--padding-md);
            border-top: 1px solid var(--border-color);
            text-align: right;
        }

        /* --- VIEW VIDEO MODAL --- */
        #viewVideoModal .modal-content { max-width: 800px; }
        
        .video-player-wrapper {
            position: relative;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            height: 0;
            overflow: hidden;
            background-color: #000;
            border-radius: var(--border-radius-sm);
        }

        .video-player-wrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .video-details h2 { margin: 16px 0; }
        .video-details .card-tags { margin-bottom: 20px; }
        
        .video-actions {
            display: flex;
            gap: 10px;
        }
        .btn-secondary {
             background-color: var(--card-bg);
        }
        #copyStatus {
            color: var(--primary-color);
            font-size: 0.9em;
            margin-left: 10px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        /* --- FLOATING ANIMATIONS --- */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .floating {
            animation: float 6s ease-in-out infinite;
        }

        .floating:nth-child(2n) {
            animation-delay: -2s;
        }

        .floating:nth-child(3n) {
            animation-delay: -4s;
        }

        /* --- DESKTOP DESIGN --- */
        @media (min-width: 769px) {
            header.app-header {
                justify-content: space-between;
                gap: 16px;
            }

            .logo {
                flex-shrink: 0;
            }

            .search-bar {
                flex: 1;
                max-width: 350px;
            }

            .controls {
                flex-shrink: 0;
            }
        }

        /* --- RESPONSIVE DESIGN --- */
        @media (max-width: 768px) {
            :root {
                --padding-md: 16px;
                --border-radius-lg: 20px;
            }

            body::before {
                background:
                    radial-gradient(circle at 50% 50%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%);
            }

            header.app-header {
                flex-direction: column;
                align-items: stretch;
                padding: 16px;
                gap: 16px;
                border-radius: 0 0 20px 20px;
            }

            .logo {
                display: flex;
                font-size: 1.4rem;
                justify-content: center;
                width: 100%;
                margin-bottom: 8px;
                order: 1;
            }

            .search-bar {
                order: 2;
                width: 100%;
            }

            #searchInput {
                padding: 14px 18px 14px 45px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .search-bar .fa-search {
                left: 18px;
            }

            .controls {
                order: 3;
                width: 100%;
                justify-content: space-between;
                gap: 12px;
            }

            .view-controls {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-template-rows: repeat(2, 1fr);
                gap: 8px;
                flex: 1;
                max-width: 200px;
            }

            .view-controls .btn-icon:nth-child(4) {
                grid-column: 1;
                grid-row: 2;
            }

            .view-controls .btn-icon:nth-child(5) {
                grid-column: 2;
                grid-row: 2;
            }

            .view-controls .btn-icon {
                padding: 12px 8px;
                font-size: 1.1rem;
                min-height: 44px; /* Better touch target */
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .view-controls .btn-icon span {
                display: none;
            }

            .btn-primary {
                padding: 12px 16px;
                font-size: 0.9rem;
                min-height: 44px;
                white-space: nowrap;
            }

            .btn-primary span {
                display: none;
            }

            .btn-primary i {
                margin-right: 0;
                font-size: 1.2rem;
            }

            .main-container {
                padding: 16px;
            }

            #video-grid {
                gap: 16px;
                padding-top: 16px;
            }

            /* Mobile Grid Adjustments */
            #video-grid.large-icons {
                grid-template-columns: 1fr;
            }

            #video-grid.small-icons {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            #video-grid.small-icons .video-card .card-content {
                padding: 12px;
            }

            #video-grid.small-icons .video-card .card-title {
                font-size: 0.9rem;
                -webkit-line-clamp: 2;
                line-clamp: 2;
            }

            #video-grid.list-view .video-card {
                flex-direction: row;
                align-items: center;
                gap: 12px;
                padding: 12px;
            }

            #video-grid.list-view .video-card .card-thumbnail {
                width: 100px;
                height: 56px;
                flex-shrink: 0;
            }

            #video-grid.list-view .video-card .card-content {
                flex: 1;
                padding: 0;
            }

            #video-grid.list-view .video-card .card-title {
                font-size: 0.9rem;
                margin-bottom: 6px;
            }

            #video-grid.detailed-view .video-card {
                gap: 12px;
            }

            #video-grid.tiles-view {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            /* Modal Improvements */
            .modal-content {
                width: 95%;
                margin: 10px;
                max-height: 85vh;
                border-radius: 16px;
            }

            .modal-header {
                padding: 16px;
            }

            .modal-body {
                padding: 16px;
            }

            .modal-footer {
                padding: 16px;
            }

            .form-group input {
                padding: 14px 16px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .video-actions {
                flex-direction: column;
                gap: 12px;
            }

            .video-actions button,
            .video-actions a {
                width: 100%;
                justify-content: center;
                padding: 14px;
            }

            /* Placeholder Message */
            .placeholder-message {
                margin: 16px 0;
                padding: 40px 20px;
            }

            .placeholder-message i {
                font-size: 3rem;
                margin-bottom: 1rem;
            }

            .placeholder-message h3 {
                font-size: 1.3rem;
            }

            /* Touch Improvements */
            .video-card {
                min-height: 44px; /* Better touch target */
            }

            .tag {
                padding: 4px 8px;
                font-size: 0.7rem;
                margin: 2px;
            }

            /* Disable hover effects on touch devices */
            @media (hover: none) {
                .video-card:hover {
                    transform: none;
                    box-shadow: 0 8px 25px var(--shadow-color);
                }

                .floating {
                    animation: none;
                }
            }
        }

    </style>
</head>
<body>

    <div id="app" data-barba="wrapper">
        <main data-barba="container" data-barba-namespace="home">

            <header class="app-header">
                <div class="logo">
                    <i class="fa-brands fa-youtube"></i> MyTube Shelf
                </div>
                <div class="search-bar">
                    <i class="fa-solid fa-search"></i>
                    <input type="text" id="searchInput" placeholder="Search by title or tag...">
                </div>
                <div class="controls">
                    <div class="view-controls" id="viewControls">
                        <button class="btn-icon" data-view="large-icons" title="Large Icons"><i class="fa-solid fa-th-large"></i></button>
                        <button class="btn-icon" data-view="small-icons" title="Small Icons"><i class="fa-solid fa-th"></i></button>
                        <button class="btn-icon" data-view="list-view" title="List View"><i class="fa-solid fa-bars"></i></button>
                        <button class="btn-icon" data-view="detailed-view" title="Detailed View"><i class="fa-solid fa-list-alt"></i></button>
                        <button class="btn-icon" data-view="tiles-view" title="Tiles View"><i class="fa-solid fa-grip-horizontal"></i></button>
                    </div>
                    <button class="btn-primary" id="addVideoButton">
                        <i class="fa-solid fa-plus"></i>
                        <span>Add Video</span>
                    </button>
                </div>
            </header>

            <div class="main-container">
                <div id="video-grid" class="large-icons">
                    <!-- Video cards will be dynamically inserted here -->
                </div>
            </div>

        </main>
    </div>

    <!-- ADD VIDEO MODAL -->
    <div id="addVideoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Add New YouTube Video</h2>
                <button class="modal-close"><i class="fa-solid fa-times"></i></button>
            </div>
            <form id="addVideoForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="videoUrl">YouTube Video Link</label>
                        <input type="text" id="videoUrl" name="videoUrl" placeholder="Paste link here..." required>
                    </div>

                    <div id="fetcher-feedback" class="form-group">
                        <label>Preview</label>
                        <div class="video-preview" id="videoPreview">
                            <p id="previewText">Paste a link to see a preview</p>
                             <lottie-player id="lottie-loader" src="https://assets2.lottiefiles.com/packages/lf20_t9gkkhz4.json" background="transparent" speed="1" style="display: none;" loop autoplay></lottie-player>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="videoTags">Tags (comma-separated)</label>
                        <input type="text" id="videoTags" name="videoTags" placeholder="e.g., Programming, Tutorial, Funny">
                        <div id="tagSuggestions" class="tag-suggestions">
                            <!-- Existing tags will appear here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn-primary" id="saveVideoButton">
                        <i class="fa-solid fa-save"></i> Save Video
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- VIEW VIDEO MODAL -->
    <div id="viewVideoModal" class="modal">
        <div class="modal-content">
             <div class="modal-header">
                <h2 class="modal-title" id="viewModalTitle">Video Details</h2>
                <button class="modal-close"><i class="fa-solid fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="video-player-wrapper" id="videoPlayerWrapper">
                    <!-- YouTube iframe will be injected here -->
                </div>
                <div class="video-details">
                    <h2 id="videoDetailTitle"></h2>
                    <div class="card-tags" id="videoDetailTags"></div>
                    <div class="video-actions">
                         <a id="openInYouTubeBtn" href="#" target="_blank" class="btn-primary btn-secondary">
                            <i class="fa-brands fa-youtube"></i> Open in YouTube
                         </a>
                         <button id="copyLinkBtn" class="btn-primary btn-secondary">
                            <i class="fa-solid fa-copy"></i> Copy Link
                            <span id="copyStatus">Copied!</span>
                         </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {

            // --- STATE MANAGEMENT & LOCALSTORAGE ---
            let videos = JSON.parse(localStorage.getItem('myTubeVideos')) || [];
            let allTags = new Set(JSON.parse(localStorage.getItem('myTubeAllTags')) || []);
            let currentViewMode = localStorage.getItem('myTubeViewMode') || 'large-icons';

            // --- DOM ELEMENT SELECTORS ---
            const videoGrid = document.getElementById('video-grid');
            const searchInput = document.getElementById('searchInput');
            const viewControls = document.getElementById('viewControls');
            
            // Add Video Modal Elements
            const addVideoButton = document.getElementById('addVideoButton');
            const addVideoModal = document.getElementById('addVideoModal');
            const addVideoForm = document.getElementById('addVideoForm');
            const videoUrlInput = document.getElementById('videoUrl');
            const videoTagsInput = document.getElementById('videoTags');
            const videoPreview = document.getElementById('videoPreview');
            const previewText = document.getElementById('previewText');
            const lottieLoader = document.getElementById('lottie-loader');
            const tagSuggestionsContainer = document.getElementById('tagSuggestions');

            // View Video Modal Elements
            const viewVideoModal = document.getElementById('viewVideoModal');
            const viewModalTitle = document.getElementById('viewModalTitle');
            const videoPlayerWrapper = document.getElementById('videoPlayerWrapper');
            const videoDetailTitle = document.getElementById('videoDetailTitle');
            const videoDetailTags = document.getElementById('videoDetailTags');
            const openInYouTubeBtn = document.getElementById('openInYouTubeBtn');
            const copyLinkBtn = document.getElementById('copyLinkBtn');
            
            const closeModalButtons = document.querySelectorAll('.modal-close');

            // --- UTILITY FUNCTIONS ---
            const saveState = () => {
                localStorage.setItem('myTubeVideos', JSON.stringify(videos));
                localStorage.setItem('myTubeAllTags', JSON.stringify(Array.from(allTags)));
                localStorage.setItem('myTubeViewMode', currentViewMode);
            };

            const getYouTubeId = (url) => {
                const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
                const match = url.match(regExp);
                return (match && match[2].length === 11) ? match[2] : null;
            };

            const capitalizeWords = (str) => {
                return str.toLowerCase().split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
            };

            // --- RENDERING FUNCTIONS ---
            const renderVideos = (videosToRender = videos) => {
                videoGrid.innerHTML = ''; // Clear grid

                if (videosToRender.length === 0) {
                     videoGrid.innerHTML = `
                        <div class="placeholder-message">
                           <i class="fas fa-video-slash"></i>
                           <h3>No Videos Found</h3>
                           <p>${videos.length === 0 ? "Click 'Add Video' to save your first video!" : "Try a different search term."}</p>
                        </div>
                    `;
                    return;
                }
                
                videosToRender.forEach((video, index) => {
                    const tagsHTML = video.tags.map(tag => `<span class="tag">${tag}</span>`).join('');
                    const cardHTML = `
                        <div class="video-card floating" data-id="${video.id}" style="animation-delay: ${index * 0.1}s">
                            <div class="card-thumbnail">
                                <img src="${video.thumbnail}" alt="${video.title}">
                            </div>
                            <div class="card-content">
                                <h3 class="card-title">${video.title}</h3>
                                <div class="card-tags">${tagsHTML}</div>
                            </div>
                        </div>
                    `;
                    videoGrid.insertAdjacentHTML('beforeend', cardHTML);
                });
                
                // Animate new cards with Motion One
                motion.animate(".video-card", 
                    { opacity: [0, 1], y: [20, 0] },
                    { duration: 0.5, delay: motion.stagger(0.05) }
                );

                // Add enhanced hover effect
                 motion.dom.on(".video-card", "hover", e => {
                    motion.animate(e.target, {
                        scale: 1.05,
                        zIndex: 10,
                        y: -12,
                        rotateX: 5,
                        rotateY: 5
                    }, { duration: 0.4, easing: [0.4, 0, 0.2, 1] });
                 });
                 motion.dom.on(".video-card", "unhover", e => {
                    motion.animate(e.target, {
                        scale: 1,
                        zIndex: 1,
                        y: 0,
                        rotateX: 0,
                        rotateY: 0
                    }, { duration: 0.4, easing: [0.4, 0, 0.2, 1] });
                });
            };

            const renderTagSuggestions = () => {
                tagSuggestionsContainer.innerHTML = '';
                const tagsToShow = Array.from(allTags).slice(0, 10); // Show up to 10
                tagsToShow.forEach(tag => {
                    const tagEl = document.createElement('span');
                    tagEl.classList.add('tag', 'tag-suggestion');
                    tagEl.textContent = tag;
                    tagEl.onclick = () => {
                        const currentTags = videoTagsInput.value.split(',').map(t => t.trim()).filter(Boolean);
                        if (!currentTags.includes(tag)) {
                            currentTags.push(tag);
                            videoTagsInput.value = currentTags.join(', ');
                        }
                    };
                    tagSuggestionsContainer.appendChild(tagEl);
                });
            };

            // --- EVENT HANDLERS ---
            
            // Open "Add Video" modal
            addVideoButton.addEventListener('click', () => {
                addVideoForm.reset();
                videoPreview.innerHTML = `<p id="previewText">Paste a link to see a preview</p>`;
                renderTagSuggestions();
                addVideoModal.classList.add('is-visible');
                videoUrlInput.focus();
            });

            // Close any modal
            closeModalButtons.forEach(button => {
                button.addEventListener('click', () => {
                    button.closest('.modal').classList.remove('is-visible');
                });
            });

            // Auto-fetch video details on paste
            let fetchTimeout;
            videoUrlInput.addEventListener('input', () => {
                clearTimeout(fetchTimeout);
                fetchTimeout = setTimeout(async () => {
                    const url = videoUrlInput.value.trim();
                    const videoId = getYouTubeId(url);

                    if (videoId) {
                        videoPreview.innerHTML = ''; // Clear preview area
                        lottieLoader.style.display = 'block'; // Show loader
                        videoPreview.appendChild(lottieLoader);
                        try {
                             // Using a public oEmbed endpoint which doesn't require an API key
                            const response = await fetch(`https://www.youtube.com/oembed?url=http://www.youtube.com/watch?v=${videoId}&format=json`);
                            if (!response.ok) throw new Error('Video not found or private.');
                            const data = await response.json();
                            
                            lottieLoader.style.display = 'none';
                            videoPreview.innerHTML = `
                                <img src="${data.thumbnail_url}" alt="Video thumbnail">
                                <p>${data.title}</p>
                            `;
                            videoPreview.dataset.title = data.title;
                            videoPreview.dataset.thumbnail = data.thumbnail_url;

                        } catch (error) {
                            lottieLoader.style.display = 'none';
                            videoPreview.innerHTML = `<p style="color: #ff4d4d;">Could not fetch video details. Check the link.</p>`;
                            delete videoPreview.dataset.title;
                        }
                    } else {
                        videoPreview.innerHTML = `<p>Paste a valid YouTube link to see a preview</p>`;
                        delete videoPreview.dataset.title;
                    }
                }, 500);
            });
            
            // Form submission to add video
            addVideoForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const videoId = getYouTubeId(videoUrlInput.value);
                const title = videoPreview.dataset.title;
                const thumbnail = videoPreview.dataset.thumbnail;

                if (!videoId || !title) {
                    alert('Please provide a valid YouTube link and wait for the preview to load.');
                    return;
                }

                const tags = videoTagsInput.value.split(',')
                    .map(tag => capitalizeWords(tag.trim()))
                    .filter(tag => tag !== '');

                const newVideo = {
                    id: videoId,
                    title: title,
                    thumbnail: thumbnail,
                    tags: tags,
                    addedAt: new Date().toISOString()
                };

                // Add only if not a duplicate
                if(!videos.some(v => v.id === newVideo.id)) {
                    videos.unshift(newVideo);
                    tags.forEach(tag => allTags.add(tag));
                    saveState();
                    renderVideos();
                    addVideoModal.classList.remove('is-visible');
                } else {
                    alert('This video is already in your library.');
                }
            });

            // Dynamic Search
            searchInput.addEventListener('input', () => {
                const searchTerm = searchInput.value.toLowerCase();
                const filteredVideos = videos.filter(video => 
                    video.title.toLowerCase().includes(searchTerm) ||
                    video.tags.some(tag => tag.toLowerCase().includes(searchTerm))
                );
                renderVideos(filteredVideos);
            });
            
            // View Mode Switching
            viewControls.addEventListener('click', (e) => {
                const button = e.target.closest('.btn-icon');
                if (button) {
                    currentViewMode = button.dataset.view;
                    videoGrid.className = '';
                    videoGrid.classList.add(currentViewMode);

                    viewControls.querySelectorAll('.btn-icon').forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    
                    saveState();
                }
            });

            // Click a video card to open View modal
            videoGrid.addEventListener('click', (e) => {
                const card = e.target.closest('.video-card');
                if (card) {
                    const videoId = card.dataset.id;
                    const video = videos.find(v => v.id === videoId);
                    
                    if (video) {
                        viewModalTitle.textContent = video.title;
                        videoDetailTitle.textContent = video.title;
                        videoPlayerWrapper.innerHTML = `<iframe src="https://www.youtube.com/embed/${video.id}?autoplay=1" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>`;
                        
                        videoDetailTags.innerHTML = video.tags.map(tag => `<span class="tag">${tag}</span>`).join('');
                        openInYouTubeBtn.href = `https://www.youtube.com/watch?v=${video.id}`;
                        copyLinkBtn.dataset.link = `https://www.youtube.com/watch?v=${video.id}`;

                        viewVideoModal.classList.add('is-visible');
                    }
                }
            });
            
            // Closing View modal needs to stop the video
            viewVideoModal.querySelector('.modal-close').addEventListener('click', () => {
                videoPlayerWrapper.innerHTML = '';
            });
            
            // Copy Link button
            copyLinkBtn.addEventListener('click', () => {
                const link = copyLinkBtn.dataset.link;
                navigator.clipboard.writeText(link).then(() => {
                    const copyStatus = document.getElementById('copyStatus');
                    copyStatus.style.opacity = '1';
                    setTimeout(() => { copyStatus.style.opacity = '0'; }, 2000);
                });
            });


            // --- INITIALIZATION ---
            const initialize = () => {
                videoGrid.classList.add(currentViewMode);
                const activeButton = viewControls.querySelector(`[data-view="${currentViewMode}"]`);
                if(activeButton) activeButton.classList.add('active');

                renderVideos();
            };
            
            initialize();
        });
    </script>
</body>
</html>